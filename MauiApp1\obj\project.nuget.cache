{"version": 2, "dgSpecHash": "zVM4xWD3NrE=", "success": true, "projectFilePath": "C:\\Users\\<USER>\\Desktop\\MauiApp1\\MauiApp1\\MauiApp1.csproj", "expectedPackageFiles": ["C:\\Users\\<USER>\\.nuget\\packages\\googlegson\\2.11.0.3\\googlegson.2.11.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration\\9.0.0\\microsoft.extensions.configuration.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.configuration.abstractions\\9.0.0\\microsoft.extensions.configuration.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection\\9.0.0\\microsoft.extensions.dependencyinjection.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.dependencyinjection.abstractions\\9.0.0\\microsoft.extensions.dependencyinjection.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging\\9.0.0\\microsoft.extensions.logging.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.abstractions\\9.0.0\\microsoft.extensions.logging.abstractions.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.logging.debug\\9.0.0\\microsoft.extensions.logging.debug.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.options\\9.0.0\\microsoft.extensions.options.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.extensions.primitives\\9.0.0\\microsoft.extensions.primitives.9.0.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.graphics.win2d\\1.2.0\\microsoft.graphics.win2d.1.2.0.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.io.recyclablememorystream\\3.0.1\\microsoft.io.recyclablememorystream.3.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls\\9.0.14\\microsoft.maui.controls.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.build.tasks\\9.0.14\\microsoft.maui.controls.build.tasks.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.core\\9.0.14\\microsoft.maui.controls.core.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.controls.xaml\\9.0.14\\microsoft.maui.controls.xaml.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.core\\9.0.14\\microsoft.maui.core.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.essentials\\9.0.14\\microsoft.maui.essentials.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics\\9.0.14\\microsoft.maui.graphics.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.graphics.win2d.winui.desktop\\9.0.14\\microsoft.maui.graphics.win2d.winui.desktop.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.maui.resizetizer\\9.0.14\\microsoft.maui.resizetizer.9.0.14.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.net.illink.tasks\\9.0.3\\microsoft.net.illink.tasks.9.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.web.webview2\\1.0.2792.45\\microsoft.web.webview2.1.0.2792.45.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.buildtools\\10.0.22621.756\\microsoft.windows.sdk.buildtools.10.0.22621.756.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windowsappsdk\\1.6.240923002\\microsoft.windowsappsdk.1.6.240923002.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide\\4.16.0.6\\xamarin.android.glide.4.16.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.annotations\\4.16.0.6\\xamarin.android.glide.annotations.4.16.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.disklrucache\\4.16.0.6\\xamarin.android.glide.disklrucache.4.16.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.android.glide.gifdecoder\\4.16.0.6\\xamarin.android.glide.gifdecoder.4.16.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.activity\\1.9.2.1\\xamarin.androidx.activity.1.9.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.activity.ktx\\1.9.2.1\\xamarin.androidx.activity.ktx.1.9.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation\\1.8.2.1\\xamarin.androidx.annotation.1.8.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation.experimental\\1.4.1.6\\xamarin.androidx.annotation.experimental.1.4.1.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.annotation.jvm\\1.8.2.1\\xamarin.androidx.annotation.jvm.1.8.2.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.appcompat\\1.7.0.3\\xamarin.androidx.appcompat.1.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.appcompat.appcompatresources\\1.7.0.3\\xamarin.androidx.appcompat.appcompatresources.1.7.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.arch.core.common\\2.2.0.13\\xamarin.androidx.arch.core.common.2.2.0.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.arch.core.runtime\\2.2.0.13\\xamarin.androidx.arch.core.runtime.2.2.0.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.browser\\1.8.0.6\\xamarin.androidx.browser.1.8.0.6.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.cardview\\1.0.0.31\\xamarin.androidx.cardview.1.0.0.31.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection\\1.4.3.1\\xamarin.androidx.collection.1.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection.jvm\\1.4.3.1\\xamarin.androidx.collection.jvm.1.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.collection.ktx\\1.4.3.1\\xamarin.androidx.collection.ktx.1.4.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.concurrent.futures\\*******\\xamarin.androidx.concurrent.futures.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.constraintlayout\\2.1.4.16\\xamarin.androidx.constraintlayout.2.1.4.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.constraintlayout.core\\1.0.4.16\\xamarin.androidx.constraintlayout.core.1.0.4.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.coordinatorlayout\\********\\xamarin.androidx.coordinatorlayout.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.core\\1.13.1.5\\xamarin.androidx.core.1.13.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.core.core.ktx\\1.13.1.5\\xamarin.androidx.core.core.ktx.1.13.1.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.cursoradapter\\********\\xamarin.androidx.cursoradapter.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.customview\\*******8\\xamarin.androidx.customview.*******8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.customview.poolingcontainer\\1.0.0.15\\xamarin.androidx.customview.poolingcontainer.1.0.0.15.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.documentfile\\1.0.1.29\\xamarin.androidx.documentfile.1.0.1.29.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.drawerlayout\\1.2.0.13\\xamarin.androidx.drawerlayout.1.2.0.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.dynamicanimation\\********\\xamarin.androidx.dynamicanimation.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.emoji2\\1.5.0.1\\xamarin.androidx.emoji2.1.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.emoji2.viewshelper\\1.5.0.1\\xamarin.androidx.emoji2.viewshelper.1.5.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.exifinterface\\1.3.7.5\\xamarin.androidx.exifinterface.1.3.7.5.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.fragment\\1.8.3.1\\xamarin.androidx.fragment.1.8.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.fragment.ktx\\1.8.3.1\\xamarin.androidx.fragment.ktx.1.8.3.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.interpolator\\********\\xamarin.androidx.interpolator.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.legacy.support.core.utils\\********\\xamarin.androidx.legacy.support.core.utils.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.common\\2.8.5.1\\xamarin.androidx.lifecycle.common.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.common.jvm\\2.8.5.1\\xamarin.androidx.lifecycle.common.jvm.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata\\2.8.5.1\\xamarin.androidx.lifecycle.livedata.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata.core\\2.8.5.1\\xamarin.androidx.lifecycle.livedata.core.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.livedata.core.ktx\\2.8.5.1\\xamarin.androidx.lifecycle.livedata.core.ktx.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.process\\2.8.5.1\\xamarin.androidx.lifecycle.process.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime\\2.8.5.1\\xamarin.androidx.lifecycle.runtime.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.android\\2.8.5.1\\xamarin.androidx.lifecycle.runtime.android.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.ktx\\2.8.5.1\\xamarin.androidx.lifecycle.runtime.ktx.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.runtime.ktx.android\\2.8.5.1\\xamarin.androidx.lifecycle.runtime.ktx.android.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel\\2.8.5.1\\xamarin.androidx.lifecycle.viewmodel.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel.android\\2.8.5.1\\xamarin.androidx.lifecycle.viewmodel.android.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodel.ktx\\2.8.5.1\\xamarin.androidx.lifecycle.viewmodel.ktx.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.lifecycle.viewmodelsavedstate\\2.8.5.1\\xamarin.androidx.lifecycle.viewmodelsavedstate.2.8.5.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.loader\\*******9\\xamarin.androidx.loader.*******9.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.localbroadcastmanager\\1.1.0.17\\xamarin.androidx.localbroadcastmanager.1.1.0.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.common\\2.8.0.1\\xamarin.androidx.navigation.common.2.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.fragment\\2.8.0.1\\xamarin.androidx.navigation.fragment.2.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.runtime\\2.8.0.1\\xamarin.androidx.navigation.runtime.2.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.navigation.ui\\2.8.0.1\\xamarin.androidx.navigation.ui.2.8.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.print\\********\\xamarin.androidx.print.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.profileinstaller.profileinstaller\\1.3.1.12\\xamarin.androidx.profileinstaller.profileinstaller.1.3.1.12.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.recyclerview\\1.3.2.8\\xamarin.androidx.recyclerview.1.3.2.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.resourceinspection.annotation\\1.0.1.17\\xamarin.androidx.resourceinspection.annotation.1.0.1.17.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.savedstate\\1.2.1.13\\xamarin.androidx.savedstate.1.2.1.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.savedstate.savedstate.ktx\\1.2.1.13\\xamarin.androidx.savedstate.savedstate.ktx.1.2.1.13.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.security.securitycrypto\\*******-alpha06\\xamarin.androidx.security.securitycrypto.*******-alpha06.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.slidingpanelayout\\********\\xamarin.androidx.slidingpanelayout.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.startup.startupruntime\\********\\xamarin.androidx.startup.startupruntime.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.swiperefreshlayout\\*******4\\xamarin.androidx.swiperefreshlayout.*******4.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.tracing.tracing\\*******\\xamarin.androidx.tracing.tracing.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.transition\\*******\\xamarin.androidx.transition.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.vectordrawable\\*******\\xamarin.androidx.vectordrawable.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.vectordrawable.animated\\*******\\xamarin.androidx.vectordrawable.animated.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.versionedparcelable\\*******\\xamarin.androidx.versionedparcelable.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.viewpager\\********\\xamarin.androidx.viewpager.********.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.viewpager2\\*******\\xamarin.androidx.viewpager2.*******.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.window\\1.3.0.3\\xamarin.androidx.window.1.3.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.androidx.window.extensions.core.core\\1.0.0.11\\xamarin.androidx.window.extensions.core.core.1.0.0.11.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.android.material\\1.11.0.3\\xamarin.google.android.material.1.11.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.code.findbugs.jsr305\\3.0.2.16\\xamarin.google.code.findbugs.jsr305.3.0.2.16.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.crypto.tink.android\\1.15.0.1\\xamarin.google.crypto.tink.android.1.15.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.errorprone.annotations\\2.30.0.1\\xamarin.google.errorprone.annotations.2.30.0.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.google.guava.listenablefuture\\1.0.0.24\\xamarin.google.guava.listenablefuture.1.0.0.24.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.jetbrains.annotations\\24.1.0.8\\xamarin.jetbrains.annotations.24.1.0.8.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlin.stdlib\\2.0.10.1\\xamarin.kotlin.stdlib.2.0.10.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlin.stdlib.common\\2.0.10.1\\xamarin.kotlin.stdlib.common.2.0.10.1.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.atomicfu\\0.25.0.3\\xamarin.kotlinx.atomicfu.0.25.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.atomicfu.jvm\\0.25.0.3\\xamarin.kotlinx.atomicfu.jvm.0.25.0.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.android\\1.8.1.3\\xamarin.kotlinx.coroutines.android.1.8.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.core\\1.8.1.3\\xamarin.kotlinx.coroutines.core.1.8.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.coroutines.core.jvm\\1.8.1.3\\xamarin.kotlinx.coroutines.core.jvm.1.8.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.serialization.core\\1.7.1.3\\xamarin.kotlinx.serialization.core.1.7.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\xamarin.kotlinx.serialization.core.jvm\\1.7.1.3\\xamarin.kotlinx.serialization.core.jvm.1.7.1.3.nupkg.sha512", "C:\\Users\\<USER>\\.nuget\\packages\\microsoft.windows.sdk.net.ref\\10.0.19041.57\\microsoft.windows.sdk.net.ref.10.0.19041.57.nupkg.sha512"], "logs": []}