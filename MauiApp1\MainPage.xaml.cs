﻿namespace MauiApp1
{
    public partial class MainPage : ContentPage
    {
        // دیکشنری برای نگه‌داری اطلاعات گل‌ها
        private Dictionary<string, FlowerInfo> flowers;

        public MainPage()
        {
            InitializeComponent();
            InitializeFlowers();
        }

        private void InitializeFlowers()
        {
            flowers = new Dictionary<string, FlowerInfo>
            {
                {
                    "رز قرمز",
                    new FlowerInfo
                    {
                        EmojiIcon = "🌹",
                        Description = "رز قرمز نماد عشق و احساسات عمیق است. این گل زیبا با رنگ قرمز پرشور، احساسات قوی و عاشقانه را بیان می‌کند."
                    }
                },
                {
                    "لاله زرد",
                    new FlowerInfo
                    {
                        EmojiIcon = "🌷",
                        Description = "لاله زرد نماد شادی و امید است. این گل بهاری با رنگ زرد درخشان، انرژی مثبت و شادابی را به همراه دارد."
                    }
                },
                {
                    "آفتابگردان",
                    new FlowerInfo
                    {
                        EmojiIcon = "🌻",
                        Description = "آفتابگردان نماد وفاداری و عشق بی‌قید و شرط است. این گل بزرگ همیشه رو به خورشید می‌چرخد و نماد امید و روشنایی است."
                    }
                },
                {
                    "مینا سفید",
                    new FlowerInfo
                    {
                        EmojiIcon = "🌼",
                        Description = "مینا سفید نماد پاکی و معصومیت است. این گل ساده و زیبا با گلبرگ‌های سفید، آرامش و صفا را القا می‌کند."
                    }
                },
                {
                    "بنفشه",
                    new FlowerInfo
                    {
                        EmojiIcon = "🌺",
                        Description = "بنفشه نماد فروتنی و تواضع است. این گل کوچک و ظریف با رنگ بنفش زیبا، احساسات ملایم و آرامش را بیان می‌کند."
                    }
                }
            };
        }

        private void OnFlowerSelected(object sender, EventArgs e)
        {
            var picker = sender as Picker;
            if (picker?.SelectedItem != null)
            {
                string selectedFlower = picker.SelectedItem.ToString();

                if (flowers.ContainsKey(selectedFlower))
                {
                    var flowerInfo = flowers[selectedFlower];

                    // تنظیم نام گل
                    FlowerNameLabel.Text = selectedFlower;

                    // تنظیم emoji گل
                    FlowerEmojiLabel.Text = flowerInfo.EmojiIcon;

                    // تنظیم توضیحات گل
                    FlowerDescriptionLabel.Text = flowerInfo.Description;
                }
            }
        }
    }

    // کلاس برای نگه‌داری اطلاعات گل
    public class FlowerInfo
    {
        public string EmojiIcon { get; set; }
        public string Description { get; set; }
    }
}
