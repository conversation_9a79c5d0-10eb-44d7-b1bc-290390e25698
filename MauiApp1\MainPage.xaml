﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             x:Class="MauiApp1.MainPage"
             Title="گالری گل‌ها">

    <ScrollView>
        <VerticalStackLayout
            Padding="30,20"
            Spacing="30">

            <!-- عنوان برنامه -->
            <Label
                Text="🌸 گالری گل‌های زیبا 🌸"
                Style="{StaticResource Headline}"
                HorizontalOptions="Center"
                SemanticProperties.HeadingLevel="Level1" />

            <!-- انتخابگر گل -->
            <Frame BackgroundColor="LightBlue"
                   Padding="15"
                   CornerRadius="10"
                   HasShadow="True">
                <StackLayout>
                    <Label Text="نوع گل را انتخاب کنید:"
                           FontSize="18"
                           FontAttributes="Bold"
                           HorizontalOptions="Center"/>

                    <Picker x:Name="FlowerPicker"
                            Title="انتخاب گل"
                            FontSize="16"
                            SelectedIndexChanged="OnFlowerSelected"
                            HorizontalOptions="FillAndExpand">
                        <Picker.Items>
                            <x:String>رز قرمز</x:String>
                            <x:String>لاله زرد</x:String>
                            <x:String>آفتابگردان</x:String>
                            <x:String>مینا سفید</x:String>
                            <x:String>بنفشه</x:String>
                        </Picker.Items>
                    </Picker>
                </StackLayout>
            </Frame>

            <!-- نمایش عکس گل -->
            <Frame BackgroundColor="White"
                   Padding="20"
                   CornerRadius="15"
                   HasShadow="True">
                <StackLayout>
                    <Label x:Name="FlowerNameLabel"
                           Text="گلی را انتخاب کنید"
                           FontSize="20"
                           FontAttributes="Bold"
                           HorizontalOptions="Center"
                           Margin="0,0,0,15"/>

                    <Label x:Name="FlowerEmojiLabel"
                           Text="🌸"
                           FontSize="120"
                           HorizontalOptions="Center"
                           VerticalOptions="Center"
                           HeightRequest="250"
                           WidthRequest="250"
                           BackgroundColor="LightGray"
                           HorizontalTextAlignment="Center"
                           VerticalTextAlignment="Center"/>

                    <Label x:Name="FlowerDescriptionLabel"
                           Text=""
                           FontSize="14"
                           HorizontalOptions="Center"
                           Margin="0,15,0,0"
                           TextColor="Gray"/>
                </StackLayout>
            </Frame>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
