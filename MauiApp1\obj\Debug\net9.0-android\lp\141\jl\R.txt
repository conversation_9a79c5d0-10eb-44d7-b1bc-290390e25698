int attr activityAction 0x0
int attr activityName 0x0
int attr alwaysExpand 0x0
int attr animationBackgroundColor 0x0
int attr clearTop 0x0
int attr finishPrimaryWithPlaceholder 0x0
int attr finishPrimaryWithSecondary 0x0
int attr finishSecondaryWithPrimary 0x0
int attr placeholderActivityName 0x0
int attr primaryActivityName 0x0
int attr secondaryActivityAction 0x0
int attr secondaryActivityName 0x0
int attr splitLayoutDirection 0x0
int attr splitMaxAspectRatioInLandscape 0x0
int attr splitMaxAspectRatioInPortrait 0x0
int attr splitMinHeightDp 0x0
int attr splitMinSmallestWidthDp 0x0
int attr splitMinWidthDp 0x0
int attr splitRatio 0x0
int attr stickyPlaceholder 0x0
int attr tag 0x0
int id adjacent 0x0
int id always 0x0
int id alwaysAllow 0x0
int id alwaysDisallow 0x0
int id androidx_window_activity_scope 0x0
int id bottomToTop 0x0
int id locale 0x0
int id ltr 0x0
int id never 0x0
int id rtl 0x0
int id topToBottom 0x0
int[] styleable ActivityFilter { 0x0, 0x0 }
int styleable ActivityFilter_activityAction 0
int styleable ActivityFilter_activityName 1
int[] styleable ActivityRule { 0x0, 0x0 }
int styleable ActivityRule_alwaysExpand 0
int styleable ActivityRule_tag 1
int[] styleable SplitPairFilter { 0x0, 0x0, 0x0 }
int styleable SplitPairFilter_primaryActivityName 0
int styleable SplitPairFilter_secondaryActivityAction 1
int styleable SplitPairFilter_secondaryActivityName 2
int[] styleable SplitPairRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPairRule_animationBackgroundColor 0
int styleable SplitPairRule_clearTop 1
int styleable SplitPairRule_finishPrimaryWithSecondary 2
int styleable SplitPairRule_finishSecondaryWithPrimary 3
int styleable SplitPairRule_splitLayoutDirection 4
int styleable SplitPairRule_splitMaxAspectRatioInLandscape 5
int styleable SplitPairRule_splitMaxAspectRatioInPortrait 6
int styleable SplitPairRule_splitMinHeightDp 7
int styleable SplitPairRule_splitMinSmallestWidthDp 8
int styleable SplitPairRule_splitMinWidthDp 9
int styleable SplitPairRule_splitRatio 10
int styleable SplitPairRule_tag 11
int[] styleable SplitPlaceholderRule { 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0, 0x0 }
int styleable SplitPlaceholderRule_animationBackgroundColor 0
int styleable SplitPlaceholderRule_finishPrimaryWithPlaceholder 1
int styleable SplitPlaceholderRule_placeholderActivityName 2
int styleable SplitPlaceholderRule_splitLayoutDirection 3
int styleable SplitPlaceholderRule_splitMaxAspectRatioInLandscape 4
int styleable SplitPlaceholderRule_splitMaxAspectRatioInPortrait 5
int styleable SplitPlaceholderRule_splitMinHeightDp 6
int styleable SplitPlaceholderRule_splitMinSmallestWidthDp 7
int styleable SplitPlaceholderRule_splitMinWidthDp 8
int styleable SplitPlaceholderRule_splitRatio 9
int styleable SplitPlaceholderRule_stickyPlaceholder 10
int styleable SplitPlaceholderRule_tag 11
